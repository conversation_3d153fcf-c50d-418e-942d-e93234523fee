import BusinessLocation from '@/models/business-location.model';
import { isValidObjectId } from 'mongoose';

export const handleBusinessLocation = async (businessName: string, location: string): Promise<string> => {
  const normalizedName = businessName.trim();
  const normalizedLocation = location.trim();

  const matchingLocation = await BusinessLocation.findOne({
    name: { $regex: `^${normalizedName}$`, $options: 'i' },
    location: { $regex: `^${normalizedLocation}$`, $options: 'i' },
    isActive: true,
  });

  if (matchingLocation) return matchingLocation._id.toString();

  const newLocation = await BusinessLocation.create({ name: normalizedName, location: normalizedLocation });
  return newLocation._id.toString();
};
