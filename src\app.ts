import dotenv from 'dotenv';
import express, { Express } from 'express';
import morgan from 'morgan';
import helmet from 'helmet';
import mongoSanitize from 'express-mongo-sanitize';
import hpp from 'hpp';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import fileUpload from 'express-fileupload';
import cors from 'cors';

import 'express-async-errors';

import notFoundMiddleware from '@/middleware/not-found.middleware';
import errorHandlerMiddleware from '@/middleware/error.middleware';
import apiLimiterMiddleware from '@/middleware/rate-limit.middleware';

import authRouter from '@/routes/auth.routes';
import userRouter from '@/routes/user.routes';
import staffRouter from '@/routes/staff.routes';
import addressRouter from '@/routes/address.routes';
import educationRouter from '@/routes/education.routes';
import profileRouter from '@/routes/profile.routes';
import enquiryRouter from '@/routes/enquiry.routes';
import businessRouter from '@/routes/business-location.routes';

import { getOriginConfig } from '@/utils/config.utils';

dotenv.config();

const app: Express = express();

app.set('trust proxy', 1);

const originConfig = getOriginConfig();

const corsOptions = {
  origin: [...originConfig.corsOrigins],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'x-retry-count'],
  preflightContinue: false,
  optionsSuccessStatus: 204,
};

// Middleware
app.use(express.static('public'));
app.use(cors(corsOptions));
app.use(morgan('dev'));
app.use(express.json());
app.use(cookieParser(process.env.JWT_SECRET));
app.use(fileUpload());

app.use(
  helmet({
    contentSecurityPolicy: false,
    xssFilter: true,
    noSniff: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
    hsts: { maxAge: 15552000, includeSubDomains: true, preload: true },
  })
);

app.use(mongoSanitize());
app.use(hpp());
app.use(compression());
app.use(apiLimiterMiddleware);

// API Routes
app.use('/api/v1/auth', authRouter);
app.use('/api/v1/users', userRouter);
app.use('/api/v1/staff', staffRouter);
app.use('/api/v1/addresses', addressRouter);
app.use('/api/v1/education', educationRouter);
app.use('/api/v1/profile', profileRouter);
app.use('/api/v1/enquiries', enquiryRouter);
app.use('/api/v1/business-locations', businessRouter);

// Error Handling
app.use(notFoundMiddleware);
app.use(errorHandlerMiddleware);

export default app;
