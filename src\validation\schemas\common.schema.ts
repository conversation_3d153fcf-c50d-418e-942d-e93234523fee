import { z } from 'zod';
import { genderMap, IGenderMap } from './maps';

export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
export const PASSWORD_REQUIREMENTS = 'Password must be at least 8 characters and include uppercase, lowercase, number and special character';
export const OBJECTID_REGEX = /^[0-9a-fA-F]{24}$/;
export const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
export const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];

// Common Schemas

// 1. Email Schema
export const emailSchema = z.string({ required_error: 'Email is required' }).email('Please enter a valid email address').trim().toLowerCase();

// 2. Phone Schema
export const phoneSchema = z.string({ required_error: 'Phone number is required' }).refine(
  (value) => {
    if (value.startsWith('91')) {
      return /^91\d{10}$/.test(value);
    }
    return true;
  },
  { message: 'Indian phone numbers must be exactly 10 digits' }
);

// 3. Profile Picture Schema
export const profilePictureSchema = z.any().optional();

// 4. Date of Birth Schema
export const dateOfBirthSchema = z.preprocess((val) => {
  if (!val) return undefined;
  try {
    return new Date(val as any);
  } catch (error) {
    return undefined;
  }
}, z.date().optional());

// 5. Gender Schema
export const genderSchema = z.enum(Object.keys(genderMap) as [IGenderMap], {
  errorMap: () => ({ message: 'Please choose gender from the options provided' }),
});

// 6. Full Name Schema
export const fullNameSchema = z.string({ required_error: 'Full name is required' }).min(2, 'Full name must be at least 2 characters');

// 7. Object ID Schema
export const objectIdSchema = z.string().regex(OBJECTID_REGEX, 'Invalid ID format');
