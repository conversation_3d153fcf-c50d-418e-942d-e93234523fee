import { Request, Response } from 'express';

// Tuition Info
export const getTuitionInfo = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'getTuitionInfo not implemented' });
};
export const createTuitionInfo = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(201).json({ message: 'createTuitionInfo not implemented' });
};
export const updateTuitionInfo = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'updateTuitionInfo not implemented' });
};
export const deleteTuitionInfo = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'deleteTuitionInfo not implemented' });
};

// Teaching Experience
export const getAllTeachingExperience = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'getAllTeachingExperience not implemented' });
};
export const createTeachingExperience = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(201).json({ message: 'createTeachingExperience not implemented' });
};
export const updateTeachingExperience = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'updateTeachingExperience not implemented' });
};
export const deleteTeachingExperience = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'deleteTeachingExperience not implemented' });
};

// Teaching Subjects
export const getAllTeachingSubjects = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'getAllTeachingSubjects not implemented' });
};
export const createTeachingSubject = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(201).json({ message: 'createTeachingSubject not implemented' });
};
export const updateTeachingSubject = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'updateTeachingSubject not implemented' });
};
export const deleteTeachingSubject = async (req: Request, res: Response) => {
  // TODO: Implement logic
  res.status(200).json({ message: 'deleteTeachingSubject not implemented' });
};
