import mongoose, { Document, Schema } from 'mongoose';
import validator from 'validator';
import bcrypt from 'bcryptjs';
import { RegisterInput } from '@/validation/schemas/auth.schema';
import { REGEX_MAP } from '@/constants';
import { IAccountStatusMap, accountStatusMap, genderMap, userTypeMap } from '@/validation/schemas/maps';

export interface IUser extends RegisterInput {
  profilePicture?: string;
  accountStatus: IAccountStatusMap;
  verification: { token?: string; isVerified: boolean; verifiedAt?: Date };
  passwordReset: { token?: string; expiry?: Date };
  otp?: { code?: string; expiry?: Date; lastAttempt?: Date; attempts?: number; requestCount?: number };
  alternativeMobile?: string;
  primaryWhatsApp?: string;
  alternativeWhatsApp?: string;
  parentGuardianFullName?: string;
}

export interface UserDocument extends IUser, Document<mongoose.Types.ObjectId> {
  comparePassword(candidatePassword: string): Promise<boolean>;
  createdAt: Date;
  updatedAt: Date;
}

const userSchema = new Schema<UserDocument>(
  {
    fullName: {
      type: String,
      required: [true, 'Please enter your full name'],
      trim: true,
      minlength: [2, 'Full name must be at least 2 characters long'],
      maxlength: [60, 'Full name cannot exceed 60 characters'],
      validate: {
        validator: function (v: string) {
          return REGEX_MAP.ALPHABETS.test(v);
        },
        message: 'Full name must contain only alphanumeric characters',
      },
    },
    profilePicture: {
      type: String,
      trim: true,
    },
    dateOfBirth: {
      type: Date,
    },
    gender: {
      type: String,
      enum: Object.keys(genderMap),
    },
    email: {
      type: String,
      lowercase: true,
      validate: [validator.isEmail, 'Please enter a valid email address'],
    },
    password: {
      type: String,
      minlength: [6, 'Password must be at least 6 characters long'],
      select: false,
    },
    phone: {
      type: String,
      trim: true,
      minlength: [3, 'Phone number must be at least 3 digits long'],
      maxlength: [20, 'Phone number cannot exceed 20 digits'],
      validate: {
        validator: function (v: string) {
          return /^\d{3,20}$/.test(v);
        },
        message: 'Please enter a valid phone number between 3 and 20 digits',
      },
    },
    userType: {
      type: String,
      enum: Object.keys(userTypeMap),
      required: [true, 'User type is required'],
    },
    accountStatus: {
      type: String,
      enum: Object.keys(accountStatusMap),
      default: 'pending',
    },
    verification: {
      token: { type: String, select: false },
      isVerified: { type: Boolean, default: false },
      verifiedAt: Date,
    },
    passwordReset: {
      token: { type: String, select: false },
      expiry: { type: Date, select: false },
    },
    otp: {
      code: { type: String, select: false },
      expiry: Date,
      lastAttempt: Date,
      attempts: { type: Number, default: 0 },
      requestCount: { type: Number, default: 0 },
    },
    alternativeMobile: {
      type: String,
      trim: true,
      minlength: [3, 'Alternative mobile number must be at least 3 digits long'],
      maxlength: [20, 'Alternative mobile number cannot exceed 20 digits'],
      validate: {
        validator: function (v: string) {
          return /^\d{3,20}$/.test(v);
        },
        message: 'Please enter a valid phone number between 3 and 20 digits',
      },
    },
    primaryWhatsApp: {
      type: String,
      trim: true,
      minlength: [3, 'WhatsApp number must be at least 3 digits long'],
      maxlength: [20, 'WhatsApp number cannot exceed 20 digits'],
      validate: {
        validator: function (v: string) {
          return /^\d{3,20}$/.test(v);
        },
        message: 'Please enter a valid phone number between 3 and 20 digits',
      },
    },
    alternativeWhatsApp: {
      type: String,
      trim: true,
      minlength: [3, 'Alternative WhatsApp number must be at least 3 digits long'],
      maxlength: [20, 'Alternative WhatsApp number cannot exceed 20 digits'],
      validate: {
        validator: function (v: string) {
          return /^\d{3,20}$/.test(v);
        },
        message: 'Please enter a valid phone number between 3 and 20 digits',
      },
    },
    location: {
      type: String,
      required: [true, 'Location is required'],
      trim: true,
      minlength: [2, 'Location must be at least 2 characters long'],
    },
    referralSource: {
      type: String,
      required: [true, 'Referral source is required'],
      trim: true,
    },
    otherReferralSource: {
      type: String,
      trim: true,
    },
    parentGuardianFullName: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

userSchema.pre<UserDocument>('save', async function (next) {
  if (!this.isModified('password') || !this.password) {
    return next();
  }
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

userSchema.methods.comparePassword = async function (this: UserDocument, candidatePassword: string): Promise<boolean> {
  if (!this.password) return false;
  return await bcrypt.compare(candidatePassword, this.password);
};

// Add compound unique indexes to allow same phone/email with different userTypes
userSchema.index({ phone: 1, userType: 1 }, { unique: true, sparse: true });
userSchema.index({ email: 1, userType: 1 }, { unique: true, sparse: true });

const User = mongoose.models.User || mongoose.model<UserDocument>('User', userSchema);

export default User;
