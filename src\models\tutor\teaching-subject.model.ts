import mongoose, { Document, Schema } from 'mongoose';
import { serviceCategoryMap, IServiceCategoryMap } from '@/validation/schemas/education/index.maps';
import { currencyMap, ICurrencyMap } from '@/validation/schemas/tutor/tuition.maps';

export interface ITeachingSubject {
  userId: mongoose.Types.ObjectId;
  serviceCategory: IServiceCategoryMap;
  amount: number;
  budget: number;
  currency: ICurrencyMap;
  allSubjects?: boolean;

  // School fields
  boardId?: mongoose.Types.ObjectId;
  classId?: mongoose.Types.ObjectId;
  subjectIds?: mongoose.Types.ObjectId[];

  // College fields
  streamId?: mongoose.Types.ObjectId;
  degreeLevelId?: mongoose.Types.ObjectId;
  degreeId?: mongoose.Types.ObjectId;
  branchId?: mongoose.Types.ObjectId;
  collegeSubjectIds?: mongoose.Types.ObjectId[];

  // Language fields
  languageTypeId?: mongoose.Types.ObjectId;
  languageId?: mongoose.Types.ObjectId;

  // Hobby fields
  hobbyTypeId?: mongoose.Types.ObjectId;
  hobbyId?: mongoose.Types.ObjectId;

  // Exam fields
  examCategoryId?: mongoose.Types.ObjectId;
  examId?: mongoose.Types.ObjectId;
  examSubjectIds?: mongoose.Types.ObjectId[];

  // IT Course fields
  courseTypeId?: mongoose.Types.ObjectId;
  courseId?: mongoose.Types.ObjectId;

  isActive: boolean;
}

export interface TeachingSubjectDocument extends ITeachingSubject, Document {
  createdAt: Date;
  updatedAt: Date;
}

const teachingSubjectSchema = new Schema<TeachingSubjectDocument>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    serviceCategory: {
      type: String,
      enum: Object.keys(serviceCategoryMap),
      required: [true, 'Service category is required'],
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
      min: [0, 'Amount must be non-negative'],
    },
    budget: {
      type: Number,
      required: [true, 'Budget is required'],
      min: [0, 'Budget must be non-negative'],
    },
    currency: {
      type: String,
      enum: Object.keys(currencyMap),
      default: 'inr',
    },

    // School fields
    allSubjects: {
      type: Boolean,
      default: false,
    },
    boardId: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'schools';
      },
    },
    classId: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'schools';
      },
    },
    subjectIds: {
      type: [Schema.Types.ObjectId],
      ref: 'Subject',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'schools';
      },
    },

    // College fields
    streamId: {
      type: Schema.Types.ObjectId,
      ref: 'Stream',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'colleges';
      },
    },
    degreeLevelId: {
      type: Schema.Types.ObjectId,
      ref: 'DegreeLevel',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'colleges';
      },
    },
    degreeId: {
      type: Schema.Types.ObjectId,
      ref: 'Degree',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'colleges';
      },
    },
    branchId: {
      type: Schema.Types.ObjectId,
      ref: 'Branch',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'colleges';
      },
    },
    collegeSubjectIds: {
      type: [Schema.Types.ObjectId],
      ref: 'CollegeSubject',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'colleges';
      },
    },

    // Language fields
    languageTypeId: {
      type: Schema.Types.ObjectId,
      ref: 'LanguageType',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'languages';
      },
    },
    languageId: {
      type: Schema.Types.ObjectId,
      ref: 'Language',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'languages';
      },
    },

    // Hobby fields
    hobbyTypeId: {
      type: Schema.Types.ObjectId,
      ref: 'HobbyType',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'hobbies';
      },
    },
    hobbyId: {
      type: Schema.Types.ObjectId,
      ref: 'Hobby',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'hobbies';
      },
    },

    // Exam fields
    examCategoryId: {
      type: Schema.Types.ObjectId,
      ref: 'ExamCategory',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'exams';
      },
    },
    examId: {
      type: Schema.Types.ObjectId,
      ref: 'Exam',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'exams';
      },
    },
    examSubjectIds: {
      type: [Schema.Types.ObjectId],
      ref: 'ExamSubject',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'exams';
      },
    },

    // IT Course fields
    courseTypeId: {
      type: Schema.Types.ObjectId,
      ref: 'CourseType',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'it_courses';
      },
    },
    courseId: {
      type: Schema.Types.ObjectId,
      ref: 'Course',
      required: function (this: ITeachingSubject) {
        return this.serviceCategory === 'it_courses';
      },
    },

    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

teachingSubjectSchema.pre('save', function (next) {
  if (this.amount < this.budget) {
    return next(new Error('Amount cannot be less than budget'));
  }
  next();
});

// TODO: make sure to test that indexes are working as expected for uniqueness

// Add indexes to optimize queries
teachingSubjectSchema.index({ serviceCategory: 1 });
teachingSubjectSchema.index({ isActive: 1 });
teachingSubjectSchema.index({ boardId: 1, classId: 1 }, { unique: true });
teachingSubjectSchema.index({ streamId: 1, degreeLevelId: 1, degreeId: 1 }, { unique: true });
teachingSubjectSchema.index({ languageTypeId: 1, languageId: 1 }, { unique: true });
teachingSubjectSchema.index({ hobbyTypeId: 1, hobbyId: 1 }, { unique: true });
teachingSubjectSchema.index({ examCategoryId: 1, examId: 1 }, { unique: true });
teachingSubjectSchema.index({ courseTypeId: 1, courseId: 1 }, { unique: true });

const TeachingSubject = mongoose.models.TeachingSubject || mongoose.model<TeachingSubjectDocument>('TeachingSubject', teachingSubjectSchema);

export default TeachingSubject;
