import { Router } from 'express';
import { getTuitionInfo, createTuitionInfo, updateTuitionInfo, deleteTuitionInfo } from '@/controllers/tutor/tuition-info.controller';
import {
  getAllTeachingExperience,
  createTeachingExperience,
  updateTeachingExperience,
  deleteTeachingExperience,
} from '@/controllers/tutor/teaching-experience.controller';
import {
  getAllTeachingSubjects,
  createTeachingSubject,
  updateTeachingSubject,
  deleteTeachingSubject,
} from '@/controllers/tutor/teaching-subject.controller';

const router = Router();

// Tuition Info
router.get('/tuition-info', getTuitionInfo);
router.post('/tuition-info', createTuitionInfo);
router.patch('/tuition-info/:id', updateTuitionInfo);
router.delete('/tuition-info/:id', deleteTuitionInfo);

// Teaching Experience
router.get('/teaching-experience', getAllTeachingExperience);
router.post('/teaching-experience', createTeachingExperience);
router.patch('/teaching-experience/:id', updateTeachingExperience);
router.delete('/teaching-experience/:id', deleteTeachingExperience);

// Teaching Subjects
router.get('/teaching-subjects', getAllTeachingSubjects);
router.post('/teaching-subjects', createTeachingSubject);
router.patch('/teaching-subjects/:id', updateTeachingSubject);
router.delete('/teaching-subjects/:id', deleteTeachingSubject);

export default router;
