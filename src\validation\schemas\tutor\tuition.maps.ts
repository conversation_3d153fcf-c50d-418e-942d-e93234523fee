export const tuitionTypeMap = {
  private: { key: 'private', label: 'Private Tuition' },
  school: { key: 'school', label: 'School' },
  college: { key: 'college', label: 'College' },
  institute: { key: 'institute', label: 'Institute' },
  other: { key: 'other', label: 'Other' },
} as const;

export type ITuitionTypeMap = keyof typeof tuitionTypeMap;

export const currencyMap = {
  inr: { key: 'inr', label: 'INR', symbol: '₹' },
  usd: { key: 'usd', label: 'USD', symbol: '$' },
} as const;

export type ICurrencyMap = keyof typeof currencyMap;
